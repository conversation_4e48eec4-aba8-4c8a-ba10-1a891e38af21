    package com.score.callmetest.ui.chat

import android.Manifest
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.PopupMenu
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityChatBinding
import com.score.callmetest.databinding.LayoutVoiceRecordingBinding
import com.score.callmetest.entity.ChatListItem
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.ContentType
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.ImCardEntity
import com.score.callmetest.entity.MessageEvents
import com.score.callmetest.entity.MessageType
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.im.ImConnectionStatus
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.UserInfo
import com.score.callmetest.network.toBroadcasterModel
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.chat.adapter.ChatMessageAdapter
import com.score.callmetest.ui.preview.MultiImagePreviewActivity
import com.score.callmetest.ui.videocall.GiftDialogFragment
import com.score.callmetest.ui.videocall.GiftSendEvent
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.CommonActionBottomSheet
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.AnimatorUtil
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.InputMethodUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.keyboard.KeyboardHeightObserver
import com.score.callmetest.util.keyboard.KeyboardHeightProvider
import timber.log.Timber
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.IntentCompat
import androidx.core.os.BundleCompat
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.ImagePickerUtils
import com.score.callmetest.util.StatusBarUtils


/**
 * 聊天页面
 */
class ChatActivity : BaseActivity<ActivityChatBinding, ChatViewModel>() {

    private lateinit var mAdapter: ChatMessageAdapter
    private var mTargetUserInfo: UserInfo? = null

    // 语音录制UI
    private lateinit var mVoiceRecordingBinding: LayoutVoiceRecordingBinding
    private var mVoiceRecordingView: View? = null

    // 是否在录音中
    private var mIsRecording = false

    // 手指按下的初始Y坐标
    private var mTouchStartY = 0f

    // 是否已经显示取消录音状态
    private var mIsShowingCancelRecording = false

    // 缓存emojiBtn、keyboardBtn图标
    private var mKeyboardDrawable: Drawable? = null
    private var mEmojiDrawable: Drawable? = null

    /**
     * 语音消息过短提示
     */
    private val mStrReCordingLess: String by lazy { getString(R.string.recording_less_1s) }

    // 记录list-size
    private var mLastListSize = 0

    private var mIsOnResume = false

    // 图片选择器
    private val mImagePickerLauncher =
        ImagePickerUtils.createImagePicker(this@ChatActivity, object :
            ImagePickerUtils.ImagePickerCallback {
            override fun onImageSelected(imageUri: Uri) {
                viewModel.sendImageMessage(imageUri)
            }

            override fun onImageSelectionFailed(error: String) {
            }
        })

    /** 是否机器人客服 */
    private var mIsRobot: Boolean? = null

    private val mScrollHideListener = object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            forceSetSoftInputKeyBoard(isShow = false, clearFocus = true)
            if (binding.emojiPicker.isVisible) {
                showOrHideEmojiPicker(false)
                showOrHideBoardContainer(false)
            }
        }
    }

    private val mScrollToBottomListener = object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                // 滑动结束
                binding.recyclerView.addOnScrollListener(mScrollHideListener)
                binding.recyclerView.removeOnScrollListener(this)
            }
        }
    }

    companion object {
        private const val EXTRA_USER = "extra_user"
        private const val IS_ROBOT = "is_robot"
        // 判断是否是取消录音的距离阈值（dp）
        private const val CANCEL_THRESHOLD_DP = 100f

        // 当前聊天用户ID，用于MessageIncomingManager判断
        @Volatile
        private var currentChatUserId: String? = null

        /**
         * 启动聊天页面
         * @param context 上下文
         * @param user 聊天对象
         */
        fun start(context: Context?, user: UserInfo?) {
            if (user == null) return
            context?.apply {
                val intent = Intent(this, ChatActivity::class.java).apply {
                    putExtra(EXTRA_USER, user)
                    putExtra(IS_ROBOT, user.userId == Constant.ROBOt_ID)
                }
                startActivity(intent)
            }
        }

        /**
         * 获取当前聊天用户ID
         * 用于MessageIncomingManager判断是否显示弹窗
         */
        fun getCurrentChatUserId(): String? {
            return currentChatUserId
        }

        /**
         * 设置当前聊天用户ID
         * 内部方法，由ChatActivity调用
         */
        internal fun setCurrentChatUserId(userId: String?) {
            currentChatUserId = userId
        }
    }

    /**
     * 是否是官方账号
     * @return [Boolean]
     */
    private fun isOfficialUser(): Boolean {
        return mIsRobot == true ||
               mTargetUserInfo == null ||
               StrategyManager.isTopOfficialUser(mTargetUserInfo!!.userId)
    }

    /**
     * 真人客服
     * @return [Boolean]
     */
    private fun isLiveService(): Boolean {
        return StrategyManager.isLiveService(mTargetUserInfo!!.userId)
    }

    /**
     * 跳转真人客服
     */
    private fun gotoLiveService() {
        UserInfoManager.getUserInfo(StrategyManager.strategyConfig?.userServiceAccountId,scope = lifecycleScope) { userInfo ->
            userInfo?.let {
                ChatActivity.start(this, it)
            }
        }
    }

    /**
     * 提供标题--兼容官方账号
     * @return [String]
     */
    private fun provideTitle(): String {
        val title = if(mIsRobot == true) getString(R.string.msg_item_service_name) else  mTargetUserInfo?.nickname ?: ""
        return title
    }

    override fun getViewBinding(): ActivityChatBinding {
        return ActivityChatBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass(): Class<ChatViewModel> {
        return ChatViewModel::class.java
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查融云SDK连接状态
        val connectionStatus = RongCloudManager.getCurrentConnectionStatus()
        if (connectionStatus != ImConnectionStatus.CONNECTED) {
            Timber.tag("dsc--").e("融云SDK未连接，当前状态：$connectionStatus")
        }

        // 强制拉最新信息，确认金币数量
        UserInfoManager.refreshMyUserInfo()

        // 初始化聊天
        viewModel.initChat(mTargetUserInfo!!)

        Timber.tag("dsc--").d("ChatActivity初始化完成，用户：${mTargetUserInfo?.nickname}")
    }

    /**
     * 初始化语音录制UI
     */
    private fun initVoiceRecordingView() {
        // 使用布局膨胀器加载语音录制UI
        val inflater = layoutInflater
        mVoiceRecordingBinding = LayoutVoiceRecordingBinding.inflate(inflater)
        mVoiceRecordingView = mVoiceRecordingBinding.root

        // 设置录音UI布局参数，让它铺满整个父布局
        val layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        mVoiceRecordingView?.layoutParams = layoutParams

        // 初始状态不显示
        mVoiceRecordingView?.visibility = View.GONE

        // 将语音录制UI添加到布局中
        if (mVoiceRecordingView != null) (binding.root as ViewGroup).addView(mVoiceRecordingView)
    }

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.toolbar)
        // 使用 Bundle.toUserInfo() 扩展方法获取用户信息

        // 获取传递的用户信息
        mTargetUserInfo = IntentCompat.getParcelableExtra<UserInfo>(intent,EXTRA_USER, UserInfo::class.java)
        mIsRobot = intent.getBooleanExtra(IS_ROBOT, true)
        if (mTargetUserInfo == null) {
            Timber.tag("dsc--").e("用户信息获取失败")
            finish()
            return
        }
        // 设置当前聊天用户ID
        setCurrentChatUserId(mTargetUserInfo?.userId)
        // 设置标题 - 直接从Intent获取的用户信息设置，确保标题显示
        binding.tvTitle.run {
            text = provideTitle()
            requestFocus()
        }

        viewModel.loadUnreadCount{
            binding.tvSum.isVisible = it > 0
            binding.tvSum.text = it.toString()
        }

        if (isOfficialUser()) {
            // 官方号不需要视频
            binding.fabVideoCall.isVisible = false
            // 官方号不需要菜单
            binding.btnMenu.visibility = View.INVISIBLE
        }else {
            // status-缓存
            val cacheStatus = UserInfoManager.getCachedStatus(currentChatUserId!!) ?: CallStatus.UNKNOWN
            viewModel.updateStatus(cacheStatus)
            updateOnlineStatus(cacheStatus)
        }
//        binding.fabVideoCall.isTouchable = false
//        binding.fabVideoCall.isEnabled = false

        if(mIsRobot == true){
            // 机器人客服
            binding.btnMenu.setImageResource(R.drawable.customer_service_black)
            binding.btnMenu.visibility = View.VISIBLE
        }

        if (StrategyManager.isReviewPkg()) {
            binding.fabGift.visibility = View.GONE
        }

        // 显示礼物按钮
        binding.fabGift.isVisible = !StrategyManager.isReviewPkg() && !isOfficialUser()

        if (!isOfficialUser() || isLiveService()) {
            // 隐藏发送按钮
            binding.btnSend.visibility = View.INVISIBLE
            // 普通用户&真人客服需要显示输入
            binding.inputLayout.isVisible = true
            // keyboard-适配
            mBoardContainer = binding.boardContainer
            updateBoardContainerHeight()

        } else {
            binding.inputLayout.isVisible = false
        }

        // 初始化消息列表
        setupRecyclerView()

        // 初始化语音录制UI
        initVoiceRecordingView()

        setEmptyView()

        // 初始化聊天列表数据
        // 添加主播card（如果不是客服且用户信息存在）
        if (!isOfficialUser() && mTargetUserInfo != null) {
            val chatListItems = mutableListOf<ChatListItem>()
            chatListItems.add(
                ChatListItem.BroadcasterCard(
                    userInfo = mTargetUserInfo!!
                )
            )
            mAdapter.submitList(chatListItems)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        binding.rootContent.click {
            InputMethodUtils.getInputLanguages()
        }

        // 返回按钮
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnBack) {
            Timber.tag("dsc--").d("返回按钮点击")
            onBackPressedDispatcher.onBackPressed()
        }

        // 菜单按钮
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnMenu) {
            Timber.tag("dsc--").d("菜单按钮点击")
            if(mIsRobot == true){
                // 机器人客服-->真人客服
                gotoLiveService()
                return@setOnGlobalDebounceClickListener
            }
            showMoreMenu()
        }

        // 发送按钮
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnSend) {
            val content = binding.etMessage.text.toString().trim()
            if (content.isNotEmpty()) {
                Timber.tag("dsc--").d("发送文本消息：$content")
                viewModel.sendTextMessage(content)
                binding.etMessage.setText("")
            }
        }

        // 下拉刷新
        setupSwipeRefresh()

        // 输入框监听
        binding.etMessage.click {
            // 如果表情面板不可见，则显示键盘
            showOrHideEmojiPicker(false)
            showOrHideBoardContainer(true)
        }
        // 防止第一次点击etMessage无效
        binding.etMessage.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                // 隐藏emoji
                showOrHideEmojiPicker(false)
                showOrHideBoardContainer(true)
            }
        }
        binding.etMessage.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                binding.btnSend.visibility =
                    if (s.toString().trim().isNotEmpty()) View.VISIBLE else View.INVISIBLE
                binding.btnImage.visibility =
                    if (s.toString().trim().isEmpty()) View.VISIBLE else View.INVISIBLE
            }
        })

        // 录音按钮
        binding.btnTalk.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 开始录音
                    startRecording()
                    // 记录初始触摸位置
                    mTouchStartY = event.rawY
                    mIsShowingCancelRecording = false
                    return@setOnTouchListener true
                }
                MotionEvent.ACTION_MOVE -> {
                    if (!mIsRecording) return@setOnTouchListener false
                    // 判断是否需要显示取消录音状态
                    val moveDistance = mTouchStartY - event.rawY
                    val cancelThreshold = DisplayUtils.dp2px(CANCEL_THRESHOLD_DP)

                    if (moveDistance > cancelThreshold && !mIsShowingCancelRecording) {
                        // 显示取消录音状态
                        showCancelRecordingState(true)
                        mIsShowingCancelRecording = true
                    } else if (moveDistance <= cancelThreshold && mIsShowingCancelRecording) {
                        // 恢复正常录音状态
                        showCancelRecordingState(false)
                        mIsShowingCancelRecording = false
                    }
                    return@setOnTouchListener true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (!mIsRecording) return@setOnTouchListener false
                    // 停止录音
                    if (mIsShowingCancelRecording) {
                        // 取消录音
                        cancelRecording()
                    } else {
                        // 完成录音并发送
                        stopAndSendRecording()
                    }
                    return@setOnTouchListener true
                }
            }
            // 不消费事件，继续传递
            false
        }

        // voice按钮
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnVoice) {
            Timber.tag("dsc--").d("voice按钮点击")

            // 先检查麦克风权限
            AppPermissionManager.checkAndRequestMicrophonePermission(
                this,
                onGranted = {
                    // 权限已授予，切换到录音界面
                    switchToVoiceMode()
                    forceSetSoftInputKeyBoard(isShow = false, clearFocus = true)
                    showOrHideEmojiPicker(false)
                    showOrHideBoardContainer(false)
                },
                onDenied = {
                    // 权限被拒绝，不切换界面
                    val s = AppPermissionManager.shouldShowRequestPermissionRationale(
                        this,
                        Manifest.permission.RECORD_AUDIO
                    )
                    if (s) {
                        Timber.d("ask") // 可以再次询问权限
                    } else {
                        Timber.d("onDenied") // 权限被永久拒绝，跳转设置页面
                        AppPermissionManager.openAppSettings()
                    }
                    //Timber.tag("dsc--").d("麦克风权限被拒绝，无法切换到录音模式")
                }
            )
        }

        // keyboard按钮
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnKeyboard) {
            Timber.tag("dsc--").d("keyboard按钮点击")
            // voice-edittext-emoji-image-显示，其他隐藏
            binding.etMessage.visibility = View.VISIBLE
            binding.btnEmoji.visibility = View.VISIBLE
            binding.btnVoice.visibility = View.VISIBLE
            binding.btnKeyboard.visibility = View.INVISIBLE
            binding.btnTalk.visibility = View.INVISIBLE
            val editStr = binding.etMessage.text.toString().trim()
            binding.btnSend.visibility = if (editStr.isNotEmpty()) View.VISIBLE else View.INVISIBLE
            binding.btnImage.visibility = if (editStr.isEmpty()) View.VISIBLE else View.INVISIBLE
        }

        // 表情按钮 - 使用全局防抖，处理连续点击不同按钮的情况
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnEmoji) {
            Timber.tag("dsc--").d("表情按钮点击")

            // 根据当前状态切换表情面板
            if (binding.emojiPicker.isVisible) {
                // 当前可见
                showOrHideEmojiPicker(false)
                forceSetSoftInputKeyBoard(isShow = true, clearFocus = false)
                if(binding.etMessage.hasFocus()) {
                    showOrHideBoardContainer(true)
                }else {
                    showOrHideBoardContainer(false)
                }
            } else {
                showOrHideEmojiPicker(true)
                forceSetSoftInputKeyBoard(isShow = false, clearFocus = false)
                showOrHideBoardContainer(true)
            }
        }


        // 图片按钮 - 使用全局防抖，处理连续点击不同按钮的情况
        ClickUtils.setOnGlobalDebounceClickListener(binding.btnImage) {
            Timber.tag("dsc--").d("图片按钮点击")
            mImagePickerLauncher.launch(ImagePickerUtils.createGalleryIntent())
        }

        // 视频通话悬浮按钮 - 使用全局防抖
        ClickUtils.setOnGlobalDebounceClickListener(binding.fabVideoCall) {
            Timber.tag("dsc--").d("视频通话悬浮按钮点击")
            goVideoCall()
        }

        // 礼物悬浮按钮 - 使用全局防抖
        ClickUtils.setOnGlobalDebounceClickListener(binding.fabGift) {
            Timber.tag("dsc--").d("礼物悬浮按钮点击")
            // 发送礼物消息
            GiftDialogFragment("chat").show(supportFragmentManager, "gift_dialog")
        }

        // emoji
        binding.emojiPicker.setOnEmojiPickedListener {
            val newText = binding.etMessage.text?.append(it.emoji)
            binding.etMessage.text = newText
        }
    }

    override fun initData() {

        // 观察加载状态
        viewModel.mIsLoading.observe(this) { isLoading ->
            binding.swipeRefreshLayout.isRefreshing = isLoading
        }

        // 观察消息列表变化
        viewModel.messages.observe(this) { messages ->
            Timber.tag("dsc--").d("消息列表更新，数量：${messages.size}")

            if(!viewModel.checkLoadMore()) {
                binding.swipeRefreshLayout.isEnabled = false
            }

            if (messages.isNotEmpty()) {
                binding.emptyView.visibility = View.GONE
                mLastListSize = mAdapter.itemCount

                // 构建聊天列表数据
                val chatListItems = mutableListOf<ChatListItem>()

                // 添加主播card（如果不是客服且用户信息存在）
                if (!isOfficialUser() && mTargetUserInfo != null) {
                    chatListItems.add(ChatListItem.BroadcasterCard(userInfo = mTargetUserInfo!!))
                }

                // 添加消息列表
                messages.forEach { message ->
                    chatListItems.add(ChatListItem.Message(message))
                }

                mAdapter.submitList(chatListItems) {
                    if (binding.swipeRefreshLayout.isRefreshing) {
                        // 下拉刷新
                        binding.swipeRefreshLayout.isRefreshing = false

                    } else if (mLastListSize == mAdapter.itemCount) {
                        // 更新某个Item---do nothings

                    } else {
                        // 不是下拉刷新--发消息、收消息
                        scrollToBottomSmoothly()

                    }
                    mLastListSize = mAdapter.itemCount
                }
            } else {
                // 没有消息时
                if(isOfficialUser()){
                    // 官方没有card
                    binding.emptyView.isVisible = true
                }else {
                    binding.emptyView.isVisible = false

                    // 构建只包含主播card和空状态的列表
                    val chatListItems = mutableListOf<ChatListItem>()

                    // 添加主播card（如果不是客服且用户信息存在）
                    if (mTargetUserInfo != null) {
                        chatListItems.add(ChatListItem.BroadcasterCard(userInfo = mTargetUserInfo!!))
                    }

                    // 添加空状态
                    chatListItems.add(ChatListItem.EmptyState)

                    mAdapter.submitList(chatListItems)
                }
                if (binding.swipeRefreshLayout.isRefreshing) {
                    binding.swipeRefreshLayout.isRefreshing = false
                }
            }
        }

        // 观察在线状态变化
        viewModel.onlineStatus.observe(this) { status ->
            Timber.tag("dsc--").d("在线状态更新：$status")
            if(isOfficialUser()){
                // 官方号不需要状态
                return@observe
            }
            updateOnlineStatus(status)
        }

        // 监听状态变化
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.NewStatusEvent::class.java
        ) { event ->
            if(isOfficialUser()){
                // 官方号不需要状态
                return@observe
            }
            event.statusMap[mTargetUserInfo?.userId]?.let { status ->
                updateOnlineStatus(status)
                viewModel.updateStatus(status)
            }
        }

        // 观察发送状态
        viewModel.sendingStatus.observe(this) { status ->
            when (status) {
                is ChatViewModel.SendStatus.Error -> {
                    // 发送失败
                    Timber.e("sendStatus---${status.errorMessage}")
                }

                is ChatViewModel.SendStatus.Success -> {
                    // 发送成功
                }

                else -> {}
            }
        }

        
        // 观察录音倒计时
        viewModel.recordTimeText.observe(this) { duration ->
            mVoiceRecordingBinding.tvRecordTime.text = getString(R.string.recording_timer, duration)
            // 更新提示文本
//            updateRecordingHint()
        }

        // 观察录音状态
        viewModel.recordingState.observe(this) { state ->
            when (state) {
                ChatViewModel.RecordingState.INITIALIZED -> {
                    // 录音初始化完成，准备好了
                }

                ChatViewModel.RecordingState.RECORDING -> {
                    // 正在录音
                }

                ChatViewModel.RecordingState.COMPLETED -> {
                    // 录音完成
                }

                ChatViewModel.RecordingState.COMPLETED_1S -> {
                    // 录音完成-但小于1s
                    ToastUtils.showShortToast(mStrReCordingLess)
                }

                ChatViewModel.RecordingState.CANCELED -> {
                    // 录音取消
                }

                ChatViewModel.RecordingState.ERROR -> {
                    // 录音错误
                    Timber.e("dsc--录音失败，请重试")
                    // 错误了就不需要这些view了
                    if (mVoiceRecordingView?.visibility != View.GONE) {
                        mVoiceRecordingView?.visibility = View.GONE
                        mVoiceRecordingBinding.recordingImg.stopAnimation()
                    }
                }
            }
        }

        // 语音播放停止
        viewModel.stopVoice.observe(this) {
            stopPlayingVoice()
        }

        // 语音下载完成监听
        EventBus.observe(this, MessageEvents.AudioDownloadOk::class.java) {
            viewModel.updateAudioDownload(it)
        }

        // 发送礼物监听--选中与否
        EventBus.observe(this, GiftSendEvent::class.java) { giftEvent ->
            if (!mIsOnResume) {
                // 其他地方送礼物---不做响音
                return@observe
            }
            // 收到发送礼物指令
            viewModel.sendGift(giftEvent.gift)
            // 退出礼物弹窗
            supportFragmentManager.findFragmentByTag("gift_dialog")?.let { fragment ->
                if (fragment is GiftDialogFragment && fragment.isVisible) {
                    fragment.dismissAllowingStateLoss()
                }
            }
            // 礼物动画展示
            showGiftAnimation(giftEvent.gift)
        }

        // 监听金币余额（使用双通道去重）
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            // 更新余额
            val availableCoins = availableCoinsMessage.coins
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
        }
    }

    /**
     * 下啦刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            if(mIsRobot == true) {
                isEnabled = false
            }
            // 设置下拉刷新的颜色
            setColorSchemeResources(R.color.refresh_loading)
            // 暂时不加
            /*setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            )*/

            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 重新加载数据
                val oldestMessageId = if(viewModel.messages.value.isNullOrEmpty()){
                    null
                } else viewModel.messages.value?.get(0)?.messageId
                viewModel.loadHistoryMessages(mTargetUserInfo?.userId,oldestMessageId)
            }
        }
    }


    //region desc-发送语音消息相关

    /**
     * 切换到录音模式
     */
    private fun switchToVoiceMode() {
        // keyboard-image-talk显示，其他隐藏
        binding.etMessage.visibility = View.INVISIBLE
        binding.btnEmoji.visibility = View.INVISIBLE
        binding.btnSend.visibility = View.INVISIBLE
        binding.btnVoice.visibility = View.INVISIBLE
        binding.btnImage.visibility = View.VISIBLE
        binding.btnKeyboard.visibility = View.VISIBLE
        binding.btnTalk.visibility = View.VISIBLE
    }

    /**
     * 开始录音
     */
    private fun startRecording() {
        if (mIsRecording) return

        // 权限检查已经在btnVoice点击时完成，这里直接开始录音
        mIsRecording = true

        // 显示录音UI
        mVoiceRecordingView?.visibility = View.VISIBLE
        mVoiceRecordingBinding.recordingParent.visibility = View.VISIBLE
        CustomUtils.playSvga(mVoiceRecordingBinding.recordingImg, "voice_recording.svga")
        mVoiceRecordingBinding.recordHoldParent.visibility = View.GONE

        // 开始录音
        viewModel.startRecording()
    }

    /**
     * 停止录音并发送消息
     */
    private fun stopAndSendRecording() {
        if (!mIsRecording) return

        mIsRecording = false

        // 隐藏录音UI
        mVoiceRecordingView?.visibility = View.GONE
        mVoiceRecordingBinding.recordingImg.stopAnimation()

        // 停止录音并发送语音消息
        viewModel.stopRecordingAndSend()
    }

    /**
     * 取消录音
     */
    private fun cancelRecording() {
        if (!mIsRecording) return

        mIsRecording = false

        // 隐藏录音UI
        mVoiceRecordingView?.visibility = View.GONE
        mVoiceRecordingBinding.recordingImg.stopAnimation()

        // 取消录音
        viewModel.cancelRecording()
    }

    /**
     * 显示取消录音状态
     */
    private fun showCancelRecordingState(showCancel: Boolean) {
        if (showCancel) {
            mVoiceRecordingBinding.recordingParent.visibility = View.GONE
            mVoiceRecordingBinding.recordHoldParent.visibility = View.VISIBLE
        } else {
            mVoiceRecordingBinding.recordingParent.visibility = View.VISIBLE
            mVoiceRecordingBinding.recordHoldParent.visibility = View.GONE
        }
    }

    /**
     * 更新录音提示文本
     */
    private fun updateRecordingHint() {
        // 从ViewModel获取剩余时间
//        val remainingTime = viewModel.getRecordingRemainingTime()
//        mVoiceRecordingBinding.tvReleaseHint.text = "Release to send ${remainingTime/1000}s"
    }

    /**
     * 停止播放语音消息
     */
    private fun stopPlayingVoice() {
        AudioPlayManager.stopPlay()
    }

    //endregion

    /**
     * 初始化RecyclerView
     */
    private fun setupRecyclerView() {
        mAdapter = ChatMessageAdapter(lifecycleScope)
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = mAdapter

        // 滚动时隐藏输入
        binding.recyclerView.addOnScrollListener(mScrollHideListener)

        // 设置消息点击监听
        mAdapter.setOnMessageClickListener { message, view ->
            // 单击消息，暂无操作
            Timber.tag("dsc--").d("消息点击：${message.content}")
            when (message.messageType) {
                MessageType.VOICE -> {
                    // 语音
                    viewModel.handleVoiceClick(message)
                }

                MessageType.LINK -> {
                    // link--充值
                    if(message.contentType == ContentType.RECHARGE_LINK && message.extra != null){
                        // 确定是充值链接
                        // 检查充值链接有效性
                        viewModel.checkRechargeLinkValid(message.extra!!) { valid,invitationId ->
                            if(valid){
                                // 充值链接有效
                                CoinRechargeDialog(RechargeSource.RECHARGE_LINK,invitationId).show(supportFragmentManager, "coin_recharge")
                            } else {
                                ToastUtils.showShortToast(CallmeApplication.context.getString(R.string.recharge_link_invalid))
                            }
                        }
                    }
                }

                else -> {}
            }
        }

        // 设置消息长按监听
        mAdapter.setOnMessageLongClickListener { message, view ->
            Timber.tag("dsc--").d("消息长按：${message.content}")
            if (message.messageType == MessageType.TEXT) {
                // 文本消息显示menu
                showMessageMenu(message, view)
            }
            true
        }

        // 设置重发监听
        mAdapter.setOnResendClickListener { message ->
            Timber.tag("dsc--").d("重发消息：${message.content}")
            viewModel.resendMessage(message)
        }

        // 设置图片点击监听
        mAdapter.setOnImageClickListener { message, view ->
            Timber.tag("dsc--").d("图片消息点击：${message.content}")
            imgPreview(imgUri = message.mediaLocalUri ?: message.mediaUri)
        }

        if (!isOfficialUser()) {
            // 设置头像点击监听
            mAdapter.setOnAvatarClickListener { message,view ->
                // 使用判断防抖，而不是装饰器模式
                if (!message.isCurrentUser && mTargetUserInfo != null) {
                    Timber.tag("dsc--").d("头像点击：${message.senderId}")

                    UserInfoManager.putCachedDrawable(
                        message.senderId,
                        view.drawable
                    )

                    val intent = Intent(this@ChatActivity, BroadcasterDetailActivity::class.java)
                    intent.putExtra(Constant.BROADCASTER_MODEL, mTargetUserInfo!!.toBroadcasterModel())
                    startActivity(intent)
                }
            }

            // 设置主播card相册图片点击监听
            mAdapter.setOnPhotoClickListener { photoUrl, position, allUrls ->
                Timber.tag("dsc--").d("相册图片点击：$photoUrl, 位置：$position")
                // 预览
                val imgUriList = allUrls.map { url ->
                    url.toUri()
                } as ArrayList<Uri>
                imgPreview(imgUriList = imgUriList, startIndex = position)
            }

            // 添加主播card点击监听
            mAdapter.setOnBcCardClickListener {
                // 跳转主播详情
                if(mTargetUserInfo != null){
                    val intent = Intent(this@ChatActivity, BroadcasterDetailActivity::class.java)
                    intent.putExtra(Constant.BROADCASTER_MODEL, mTargetUserInfo!!.toBroadcasterModel())
                    startActivity(intent)
                }
            }
        }

        // 设置通话记录点击监听
        mAdapter.setOnCallRecordClickListener { message ->
            Timber.tag("dsc--").d("通话记录点击：${message.messageId}, 类型：${message.contentType}")
            goVideoCall()
        }

        // 设置FAQ问题点击监听
        mAdapter.setOnFaqQuestionClickListener { faqCode, message ->
            Timber.tag("dsc--").d("FAQ问题点击：faqCode=$faqCode")
            // 通过ViewModel处理FAQ点击事件：用户发送问题，客服自动回复答案
            viewModel.handleFaqQuestionClick(faqCode){ faqInfo ->
                when (faqInfo.handleType) {
                    1 -> {
                        gotoLiveService()
                    }
                    2 -> {
                        // todo dsc-- 跳转提出建议页
                    }
                    3 -> {
                        // 跳转充值
                        toRechargePage(faqInfo.toUrl)
                    }
                    else -> {
                        // nothing
                    }
                }
            }
        }

        // 设置充值卡片查看详情点击监听
        mAdapter.setOnRechargeCardClickListener { message ->
            Timber.tag("dsc--").d("充值卡片查看详情点击：${message.content}")
            val cardEntity = Gson().fromJson(message.content, ImCardEntity::class.java)
            // 处理查看详情点击事件
            if(cardEntity.tppUrl.isNullOrBlank()){
                return@setOnRechargeCardClickListener
            }
            toRechargePage(cardEntity.tppUrl)
        }
    }

    private fun scrollToBottom() {
        binding.recyclerView.removeOnScrollListener(mScrollHideListener)
        binding.recyclerView.addOnScrollListener(mScrollToBottomListener)
        binding.recyclerView.postDelayed( {
            binding.recyclerView.smoothScrollToPosition(mAdapter.itemCount - 1)
        },1000L)
    }

    /**
     * 平滑滚动到底部，确保最后一个item完全显示
     * 使用LinearLayoutManager的scrollToPositionWithOffset方法确保完全显示
     */
    private fun scrollToBottomSmoothly() {
        if (mAdapter.itemCount == 0) return

        binding.recyclerView.post {
            try {
                val layoutManager = binding.recyclerView.layoutManager as? LinearLayoutManager
                if (layoutManager != null) {
                    val lastPosition = mAdapter.itemCount - 1

                    // 先尝试使用scrollToPositionWithOffset确保完全显示
                    layoutManager.scrollToPositionWithOffset(lastPosition, 0)

                    // 然后使用post再次确保滚动完成后item完全可见
                    binding.recyclerView.post {
                        // 检查最后一个item是否完全可见
                        val lastVisiblePosition = layoutManager.findLastCompletelyVisibleItemPosition()
                        if (lastVisiblePosition < lastPosition) {
                            // 如果最后一个item没有完全可见，再次滚动
                            binding.recyclerView.smoothScrollToPosition(lastPosition)
                        }
                    }
                } else {
                    // 如果不是LinearLayoutManager，使用默认方法
                    binding.recyclerView.smoothScrollToPosition(mAdapter.itemCount - 1)
                }
            } catch (e: Exception) {
                Timber.e(e, "滚动到底部失败")
                // 发生异常时使用默认方法
                binding.recyclerView.smoothScrollToPosition(mAdapter.itemCount - 1)
            }
        }
    }




    /**
     * 更新在线状态UI
     */
    private fun updateOnlineStatus(status: String) {
        val (color, text) = when (status) {
            CallStatus.ONLINE, CallStatus.AVAILABLE -> Pair(R.color.msg_online, "在线")
            CallStatus.BUSY -> Pair(R.color.msg_busy, "忙碌")
            CallStatus.IN_CALL -> Pair(R.color.msg_incall, "通话中")
            CallStatus.OFFLINE -> Pair(R.color.msg_offline, "离线")
            else -> Pair(null, "UNKNOWN")
        }

        // 在线显示正常videoCallFloat
        binding.fabVideoCall.apply {
            val enableCall = status == CallStatus.ONLINE || status == CallStatus.AVAILABLE
//            isTouchable = enableCall
//            isEnabled = enableCall
            val resId = if(enableCall) {
                R.drawable.chat_video
            }else {
                R.drawable.video_gray
            }
            setImageResource(resId)
        }

        if (color != null) {
            binding.onlineStatusDot.background.setTint(ContextCompat.getColor(this, color))
        }
        binding.onlineStatusDot.isVisible = color != null
    }



    /**
     * 跳转充值页面
     */
    private fun toRechargePage(url: String) {
        // 跳转充值
        val tppType = AppConfigManager.getConfigIntValue("tpp_open_type")
        if(tppType == 1){
            // 外部浏览器充值
            ActivityUtils.openExternalWeb(this@ChatActivity, url.toUri())
        }else {
            // webview充值
            ActivityUtils.startActivity(this@ChatActivity, WebViewActivity::class.java,
                Bundle().apply {
                    putString("url", url)
                    putString(WebViewActivity.FROM, "recharge_card")
                }
                ,false)
        }
    }

    /**
     * 开启视频通话
     */
    private fun goVideoCall(){
        // 检查unitPrice是否有效
        val unitPrice = mTargetUserInfo!!.unitPrice ?: 0
        if (unitPrice < 0) {
            return
        }
        if (!StrategyManager.isReviewPkg()) {
            // 判断金币是否足够
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < unitPrice) {
                // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                val dialog = InsufficientBalanceDialog.newInstance(
                    unitPrice,
                    RechargeSource.CONVERSATION.value()
                )
                dialog.show(supportFragmentManager, "insufficient_balance")
                return
            }
        }

        // 检查网络
        if (!SocketManager.instance.isConnected()) {
            ToastUtils.showToast(getString(R.string.long_connection_network_offline))
            return
        }
        // 获取在线状态
        UserInfoManager.loadOnlineStatus(
            lifecycleScope, mTargetUserInfo!!.userId!!
        ) { status, error ->
            runOnUiThread {
                if (error == null && status != null) {
                    if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                        val statusText = CallStatus.getDisplayText(status)
                        val message = getString(R.string.user_status_not_available, statusText)
                        ToastUtils.showToast(message)
                        return@runOnUiThread
                    }

                    // 在线状态检查通过后，再请求权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        this@ChatActivity,
                        onGranted = {
                            // 使用VideoCallManager发起视频通话
                            VideoCallActivity.startOutgoing(
                                context = this@ChatActivity,
                                userId = mTargetUserInfo!!.userId!!,
                                avatarUrl = mTargetUserInfo!!.avatarThumbUrl.toString(),
                                nickname = mTargetUserInfo!!.nickname.toString(),
                                age = mTargetUserInfo!!.age.toString(),
                                country = mTargetUserInfo!!.country.toString(),
                                unitPrice = mTargetUserInfo!!.unitPrice.toString()
                            )
                        },
                        onDenied = { deniedPermissions, permanentlyDeniedPermissions ->
                        }
                    )
                } else {
                    ToastUtils.showToast(getString(R.string.failed_to_get_user_status))
                }
            }
        }
    }

    /**
     * img预览
     *
     * imgUri 和 imgUriList必须要有一个，如果2个值都传，则以imgUriList为主
     *
     * @param [imgUri] img uri
     * @param [imgUriList] Img uri列表
     * @param [startIndex] 开始指数
     */
    private fun imgPreview(
        imgUri: Uri? = null,
        imgUriList: ArrayList<Uri>? = null,
        startIndex: Int = 0
    ) {
        if (imgUri == null && imgUriList == null) {
            return
        }
        val intent = Intent(this@ChatActivity, MultiImagePreviewActivity::class.java)
        val uriList = imgUriList ?: arrayListOf(imgUri)
        intent.putParcelableArrayListExtra("imageUris", uriList)
        intent.putExtra("startIndex", startIndex)
        startActivity(intent)
    }

    // region desc-展示礼物动画

    private val DELAY_FADE_OUT = 1000L
    private val FADE_DURATION = 600L
    private val ROTATE_DURATION = 3000L
    private val mScaleRunner = Runnable {
        mScaleAnimatorSet = AnimatorUtil.scale(binding.giftAnimationImage) {
            binding.giftAnimationImage.postOnAnimationDelayed(mFadeOutRunner, DELAY_FADE_OUT)
            binding.giftAnimationBgImage.postOnAnimationDelayed(
                mRotateAndFadeoutRunnable,
                DELAY_FADE_OUT
            )
        }
    }

    private val mFadeOutRunner = Runnable {
        mFadeOutAnimator = AnimatorUtil.fadeOut(binding.giftAnimationImage, FADE_DURATION) {
            // 礼物动画结束
            cancelGiftImageAnimations()
            // 避免还在显示手动调用gone
            binding.giftAnimationImage.visibility = View.GONE

        }
    }
    private val mRotateRunner = Runnable {
        mRotateAnimator =
            AnimatorUtil.rotate(binding.giftAnimationBgImage, duration = ROTATE_DURATION)
    }
    private val mRotateAndFadeoutRunnable = Runnable {
        mRotateAndFadeoutAnimator = AnimatorUtil.rotateAndFadeOut(
            binding.giftAnimationBgImage,
            fadeDuration = FADE_DURATION,
            rotateDuration = ROTATE_DURATION
        ) {
            // 礼物背景动画结束
            cancelGiftBgAnimations()
            // 避免还在显示手动调用gone
            binding.giftAnimationBgImage.visibility = View.GONE
        }
    }

    private var mScaleAnimatorSet: AnimatorSet? = null
    private var mFadeOutAnimator: ObjectAnimator? = null
    private var mRotateAnimator: ObjectAnimator? = null
    private var mRotateAndFadeoutAnimator: AnimatorSet? = null

    /**
     * 展示礼物动画
     * @param [giftInfo] 礼物信息
     */
    private fun showGiftAnimation(giftInfo: GiftInfo) {
        // 先隐藏，准备动画
        binding.giftAnimationImage.visibility = View.GONE
        binding.giftAnimationBgImage.visibility = View.GONE
        cancelAllAnimations()
        // giftUri
        /*val giftUri = giftInfo.iconThumbPath ?: giftInfo.iconPath ?: run {
            // 通过name来寻找本地png
            val name = giftInfo.code ?: ""
            CustomUtils.getGiftResIdByName(name)
        }*/
        // 确定只使用本地礼物资源
        // 通过name来寻找本地png
        val name = giftInfo.code ?: ""
        val giftUri = CustomUtils.getGiftResIdById(name)
        // 加载图片
        GlideUtils.load(
            url = giftUri,
            view = binding.giftAnimationImage
        )
        // 启动动画
        binding.giftAnimationBgImage.postOnAnimation(mRotateRunner)
        binding.giftAnimationImage.postOnAnimation(mScaleRunner)
    }


    /**
     * 取消所有正在进行的动画
     */
    private fun cancelAllAnimations() {
        cancelGiftImageAnimations()
        cancelGiftBgAnimations()
    }

    /**
     * 取消礼物图片动画
     */
    private fun cancelGiftImageAnimations() {
        mScaleAnimatorSet?.run {
            cancel()
            removeAllListeners()
        }
        mScaleAnimatorSet = null
        mFadeOutAnimator?.run {
            cancel()
            removeAllListeners()
        }
        mFadeOutAnimator = null
        binding.giftAnimationImage.run {
            removeCallbacks(mScaleRunner)
            removeCallbacks(mFadeOutRunner)
            // 确保完全重置视图属性
            scaleX = 1f
            scaleY = 1f
            alpha = 1f
        }
    }

    /**
     * 取消礼物背景动画
     */
    private fun cancelGiftBgAnimations() {
        mRotateAnimator?.run {
            cancel()
            removeAllListeners()
        }
        mRotateAnimator = null
        mRotateAndFadeoutAnimator?.run {
            cancel()
            removeAllListeners()
        }
        mRotateAndFadeoutAnimator = null
        binding.giftAnimationBgImage.run {
            removeCallbacks(mRotateRunner)
            removeCallbacks(mRotateAndFadeoutRunnable)
            // 重置旋转角度
            rotation = 0f
        }
    }

    // endregion

    // region desc-底部keyboard相关

    // 键盘高度提供者相关变量
    private var keyboardHeightProvider: KeyboardHeightProvider? = null
    private var mBoardContainer: View? = null

    // 键盘高度观察者
    private val mKeyboardHeightObserver = object : KeyboardHeightObserver {
        override fun onKeyboardHeightChanged(orientation: Int, isOpen: Boolean, keyboardHeight: Int) {
            if (isOpen) {
                /*val outRect = Rect()
                window.decorView.getWindowVisibleDisplayFrame(outRect)
                Timber.d("statusbar高度: ${StatusBarUtils.getStatusBarHeight(this@ChatActivity)}")
                Timber.d("键盘高度: ${keyboardHeight}")
                Timber.d("bottom: ${outRect.bottom}")
                Timber.d("应用区域高度: ${outRect.height()}")
                Timber.d("屏幕物理高度: ${DeviceUtils.getScreenHeight( this@ChatActivity)}")
                // 这个navigation高度不准，还得看rect
                Timber.d("navigation高度: ${StatusBarUtils.getNavigationBarHeight(this@ChatActivity)}")
                // 这个准
                val navBarHeight = DeviceUtils.getScreenHeight(this@ChatActivity) - outRect.bottom*/

                val saveKeyBoardHeight = SharePreferenceUtil.getSaveKeyBoardHeight(orientation)
                if (saveKeyBoardHeight != keyboardHeight) {
                    SharePreferenceUtil.saveKeyboardHeight( orientation, keyboardHeight)
                    updateBoardContainerHeight()
                }
                showOrHideBoardContainer(true)
                scrollToBottom()
            } else {
                showOrHideBoardContainer(false)
            }
        }
    }


    fun showOrHideBoardContainer(isShow: Boolean){
//        mBoardContainer?.isVisible = isShow
        if(binding.emojiPicker.isVisible){
            // emoji还需要
            // 不管isShow--都要显示
            mBoardContainer?.isVisible = true
        }else {
            mBoardContainer?.isVisible = isShow
        }
    }

    /**
     * 显示或隐藏表情选择器
     * @param [isShow] 是否显示
     */
    private fun showOrHideEmojiPicker(isShow: Boolean) {
        // 初始化表情和键盘图标
        if (mKeyboardDrawable == null) {
            mKeyboardDrawable = binding.btnKeyboard.drawable
        }
        if (mEmojiDrawable == null) {
            mEmojiDrawable = binding.btnEmoji.drawable
        }
        //
        if (isShow) {
            binding.btnEmoji.setImageDrawable(mKeyboardDrawable)
            binding.emojiPicker.visibility = View.VISIBLE

            scrollToBottom()

        } else {
            binding.btnEmoji.setImageDrawable(mEmojiDrawable)
            binding.emojiPicker.visibility = View.GONE
        }
    }

    /**
     * 检查是否应该使用键盘高度提供者
     * @return true if should use keyboard height provider
     */
    private fun useKeyboardHeightProvider(): Boolean {
        if(!binding.inputLayout.isVisible){
            // 输入框都没了--不需要这个
            return false
        }
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            !isInMultiWindowMode
        } else {
            true
        }
    }

    /**
     * 更新面板容器高度
     */
    private fun updateBoardContainerHeight() {
        if (!useKeyboardHeightProvider()) {
            return
        }
        val saveKeyboardHeight = SharePreferenceUtil.getSaveKeyBoardHeight(resources.configuration.orientation)
        val layoutParams = mBoardContainer?.layoutParams ?: return
        if (saveKeyboardHeight <= 0
            && layoutParams.height != resources.getDimensionPixelSize(R.dimen.board_height)
        ) {
            layoutParams.height = resources.getDimensionPixelSize(R.dimen.board_height)
            mBoardContainer?.layoutParams = layoutParams
        } else if (saveKeyboardHeight > 0 && layoutParams.height != saveKeyboardHeight) {
            layoutParams.height = saveKeyboardHeight
            mBoardContainer?.layoutParams = layoutParams
        }
    }

    /**
     * 强制设置软输入键盘
     * @param [isShow] 是显示
     * @param [clearFocus] 明确重点
     */
    private fun forceSetSoftInputKeyBoard(isShow: Boolean, clearFocus: Boolean = true) {
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        if (isShow) {
            // 请求焦点并显示键盘
            binding.etMessage.requestFocus()

            imm.showSoftInput(binding.etMessage, InputMethodManager.SHOW_IMPLICIT)
        } else {
            // 隐藏键盘
            imm.hideSoftInputFromWindow(binding.etMessage.windowToken, 0)
            if (clearFocus) {
                binding.etMessage.clearFocus()
            }
        }

    }

    // endregion

    private fun setEmptyView() {
        // 确保ViewStub已经被inflate
        if (binding.emptyView.parent != null) { // 检查是否还未inflate
            binding.emptyView.inflate()
        }

        // 现在可以安全地访问inflated的视图
        val emptyLayout = binding.root.findViewById<View>(R.id.layout_empty_rv_parent)
        val hintTv = emptyLayout.findViewById<TextView>(R.id.layout_empty_rv_bg_hint_tv)
        hintTv.text = getString(R.string.chat_no_messages)
    }


    /**
     * 显示消息长按菜单
     */
    private fun showMessageMenu(message: ChatMessageEntity, anchorView: View) {
        // 防止快速连续弹出多个菜单
        if (ClickUtils.isFastClickGlobal()) {
            Timber.tag("dsc--").d("快速点击被拦截：显示消息菜单")
            return
        }

        Timber.tag("dsc--").d("显示消息菜单：${message.content}")
        val popupMenu = PopupMenu(this, anchorView)
        val inflater = popupMenu.menuInflater
        inflater.inflate(R.menu.menu_chat_message, popupMenu.menu)

        // 目前只需要copy
        popupMenu.menu.findItem(R.id.menu_forward).isVisible = false
        popupMenu.menu.findItem(R.id.menu_reply).isVisible = false
        popupMenu.menu.findItem(R.id.menu_delete).isVisible = false

        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.menu_copy -> {
                    // 复制消息内容
                    val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val clip = ClipData.newPlainText("message", message.content)
                    clipboard.setPrimaryClip(clip)
                    Timber.tag("dsc--").d("复制消息：${message.content}")
                    ToastUtils.showShortToast(CallmeApplication.context.getString(R.string.copied_to_clipboard))
                    true
                }
                /*R.id.menu_forward -> {
                    // TODO: 转发消息
                    Timber.tag("dsc--").d("转发消息：${message.content}")
//                    ToastUtils.showShortToast("转发功能暂未实现")
                    true
                }
                R.id.menu_reply -> {
                    // TODO: 回复消息
                    Timber.tag("dsc--").d("回复消息：${message.content}")
//                    ToastUtils.showShortToast("回复功能暂未实现")
                    true
                }
                R.id.menu_delete -> {
                    // TODO: 删除消息
                    Timber.tag("dsc--").d("删除消息：${message.content}")
//                    ToastUtils.showShortToast("删除功能暂未实现")
                    true
                }*/
                else -> false
            }
        }
        popupMenu.show()
    }


    // region desc-更多--关注、拉黑、举报
    /**
     * 显示更多菜单
     */
    private fun showMoreMenu() {
        if (mTargetUserInfo == null) return
        val isFollowed = mTargetUserInfo?.isFriend == true
        val isBlocked = mTargetUserInfo?.isBlock == true
        // 使用通用底部对话框组件
        val sheet = CommonActionBottomSheet.builder()
        // 只有已关注时才显示取消关注选项
        if (isFollowed) {
            sheet.addAction(getString(R.string.str_btn_unfollow)) {
                Timber.tag("dsc--").d("取消关注用户：${mTargetUserInfo?.nickname}")
                unfollowUser()
            }
        } else {
            sheet.addAction(getString(R.string.str_btn_follow)) {
                Timber.tag("dsc--").d("关注用户：${mTargetUserInfo?.nickname}")
                followUser()
            }
        }

        // 根据拉黑状态显示不同的选项
        if (isBlocked) {
            sheet.addAction(getString(R.string.str_btn_unblock)) {
                Timber.tag("dsc--").d("取消拉黑用户：${mTargetUserInfo?.nickname}")
                unblockUser()
            }
        } else {
            sheet.addAction(getString(R.string.str_btn_block)) {
                Timber.tag("dsc--").d("拉黑用户：${mTargetUserInfo?.nickname}")
                blockUser()
            }
        }

        // 举报用户
        sheet.addAction(getString(R.string.str_btn_report)) {
            Timber.tag("dsc--").d("举报用户：${mTargetUserInfo?.nickname}")
            showReportDialog()
        }


        sheet.build().show(supportFragmentManager, "ChatMoreOptionsSheet")
    }

    /**
     * 显示举报对话框
     */
    private fun showReportDialog() {
        val reportOptions = resources.getStringArray(R.array.report_reason)
        /*val reportOptions = arrayOf(
            "Abusive Behavior", "Cheat Behavior", "Pornography Behavior",
            "Bloody Violence", "Harassment Behavior", "Others"
        )*/

        // 创建举报选项的底部对话框
        val reportSheet = CommonActionBottomSheet.builder()
//            .setTitle("举报原因")

        // 添加所有举报选项
        reportOptions.forEachIndexed { index, option ->
            reportSheet.addAction(option) {
                // 处理举报逻辑
                val userId = mTargetUserInfo?.userId ?: return@addAction
                val complainSub = reportOptions[index]

                Timber.tag("dsc--").d("举报用户：$userId，原因：$complainSub")
                viewModel.reportUser(userId,  complainSub) { success ->
                    runOnUiThread {
                        if (success) {
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.report_success))
                        } else {
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.report_failed))
                        }
                    }
                }
            }
        }

        reportSheet.build().show(supportFragmentManager, "ReportOptionsSheet")
    }

    /**
     * 拉黑用户
     */
    private fun blockUser() {
        val userId = mTargetUserInfo?.userId ?: return
        viewModel.block(userId) { success ->
            runOnUiThread {
                if (success) {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.block_successfully))
                    mTargetUserInfo?.isBlock = true
                    UserInfoManager.putCachedUserInfo(userId, mTargetUserInfo)
                    // TODO: 全局屏蔽该主播相关内容（墙、消息、匹配、电话等）
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.block_failed))
                }
            }
        }
    }

    private fun unblockUser() {
        val userId = mTargetUserInfo?.userId ?: return
        viewModel.unblock(userId) { success ->
            runOnUiThread {
                if (success) {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unblock_successfully))
                    mTargetUserInfo?.isBlock = false
                    UserInfoManager.putCachedUserInfo(userId, mTargetUserInfo)
                    // TODO: 取消全局屏蔽该主播
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unblock_failed))
                }
            }
        }
    }

    /**
     * 关注用户
     */
    private fun followUser() {
        val userId = mTargetUserInfo?.userId ?: return
        viewModel.follow(userId) { success ->
            runOnUiThread {
                if (success) {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_success))
                    mTargetUserInfo?.isFriend = true
                    UserInfoManager.putCachedUserInfo(userId, mTargetUserInfo)
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_failed))
                }
            }
        }
    }

    private fun unfollowUser() {
        val userId = mTargetUserInfo?.userId ?: return
        viewModel.unfollow(userId) { success ->
            runOnUiThread {
                if (success) {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unfollow_success))
                    mTargetUserInfo?.isFriend = false
                    UserInfoManager.putCachedUserInfo(userId, mTargetUserInfo)
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unfollow_failed))
                }
            }
        }
    }

    // endregion

    override fun onResume() {
        super.onResume()
        mIsOnResume = true
        if (useKeyboardHeightProvider()) {
            if(keyboardHeightProvider == null){
                keyboardHeightProvider = KeyboardHeightProvider(this@ChatActivity).apply {
                    setKeyboardHeightObserver(mKeyboardHeightObserver)
                }
            }
            binding.root.post {
                keyboardHeightProvider?.start()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        if(useKeyboardHeightProvider()){
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        }else {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }

    }

    override fun onPause() {
        super.onPause()
        mIsOnResume = false
        // 停止键盘高度提供者
        keyboardHeightProvider?.stop()
        // 停止所有语音播放
        stopPlayingVoice()
        // 取消所有动画
        cancelAllAnimations()
    }


    override fun onDestroy() {
        super.onDestroy()
        viewModel.clear()
        // 清理当前聊天用户ID
        setCurrentChatUserId(null)
        // 停止所有语音播放
        stopPlayingVoice()
        // 取消所有动画
        cancelAllAnimations()
        mVoiceRecordingBinding.recordingImg.clear()
        Timber.tag("dsc--").d("ChatActivity销毁，清理当前聊天用户ID")
    }
}