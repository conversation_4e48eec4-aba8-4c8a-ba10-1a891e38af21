package com.score.callmetest.ui.mine.mycard

import android.content.Intent
import android.graphics.Color
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMatchCardBinding
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

/**
 * Match Card Fragment - 显示匹配卡片相关信息
 */
class MatchCardFragment : BaseFragment<FragmentMatchCardBinding, MyCardViewModel>() {

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentMatchCardBinding {
        return FragmentMatchCardBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass(): Class<MyCardViewModel> = MyCardViewModel::class.java

    override fun initView() {
        super.initView()
        // 初始化视图
        setupViews()
    }

    override fun onResume() {
        super.onResume()
        // 每次页面显示时更新卡片数量
        updateCardCount()
    }

    private fun setupViews() {
        // 设置卡片数量等信息
        updateCardCount()

        binding.btnMatchCard.background = DrawableUtils.createRoundRectDrawable(
            "#AA000000".toColorInt(),
            DisplayUtils.dp2pxInternalFloat(19f)
        )

        // 设置文本样式
        setupTextStyles()
    }

    /**
     * 更新卡片数量显示
     * 从 FlashChatManager 获取卡片数量，确保数据一致性
     */
    private fun updateCardCount() {
        val cardCount = FlashChatManager.matchFreeTimes
        binding.tvMyCardCount.text = cardCount.toString()
    }

    private fun setupTextStyles() {
        // 设置 tv_talk_time_content1 中的 "20 seconds" 加粗黑色
        setupTalkTimeContent1()

        // 设置 tv_talk_time_content2 中的 "Message & Call History" 加粗橙色下划线可点击
        setupTalkTimeContent2()

        // 设置 tv_how_to_get_content1 中的文本样式
        setupHowToGetContent1()
    }

    private fun setupTalkTimeContent1() {
        val fullText = getString(com.score.callmetest.R.string.what_is_talk_time_content1)
        val spannableString = SpannableString(fullText)

        // 查找 "20 seconds" 文本
        val targetText = "20 seconds"
        val startIndex = fullText.indexOf(targetText)
        if (startIndex != -1) {
            val endIndex = startIndex + targetText.length

            // 设置加粗
            spannableString.setSpan(
                StyleSpan(android.graphics.Typeface.BOLD),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置黑色
            spannableString.setSpan(
                ForegroundColorSpan(Color.BLACK),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.tvTalkTimeContent1.text = spannableString
    }

    private fun setupTalkTimeContent2() {
        val fullText = getString(com.score.callmetest.R.string.what_is_talk_time_content2)
        val spannableString = SpannableString(fullText)

        // 查找 "Message & Call History" 文本
        val targetText = "Message & Call History"
        val startIndex = fullText.indexOf(targetText)
        if (startIndex != -1) {
            val endIndex = startIndex + targetText.length

            // 设置加粗
            spannableString.setSpan(
                StyleSpan(android.graphics.Typeface.BOLD),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置橙色 #FF8C00
            spannableString.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF8C00")),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置下划线
            spannableString.setSpan(
                UnderlineSpan(),
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置点击事件
            val clickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    // 跳转到MessageFragment
                    val currentActivity = activity
                    if (currentActivity is MyCardActivity) {
                        // 在MyCardActivity中，需要跳转到MainActivity的消息页面
                        val intent = Intent(currentActivity, MainActivity::class.java).apply {
                            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                            putExtra("target_tab_index", 1) // 1 = MessageFragment
                        }
                        currentActivity.startActivity(intent)
                        currentActivity.finish()
                    } else {
                        // 如果已经在MainActivity中，直接切换Tab
                        (currentActivity as? MainActivity)?.switchToTab(1)
                    }
                }

                override fun updateDrawState(ds: android.text.TextPaint) {
                    super.updateDrawState(ds)
                    ds.color = Color.parseColor("#FF8C00")
                    ds.isUnderlineText = true
                }
            }

            spannableString.setSpan(
                clickableSpan,
                startIndex,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.tvTalkTimeContent2.text = spannableString
        binding.tvTalkTimeContent2.movementMethod = LinkMovementMethod.getInstance()
    }

    private fun setupHowToGetContent1() {
        // 由于字符串资源中的文本可能不完整，我们直接设置完整的文本
        val fullText = getString(R.string.how_to_get_it_content1)
        val spannableString = SpannableString(fullText)

        // 查找 "receive 2 Match Cards" 文本
        val targetText1 = "receive 2 Match Cards"
        val startIndex1 = fullText.indexOf(targetText1)
        if (startIndex1 != -1) {
            val endIndex1 = startIndex1 + targetText1.length

            // 设置加粗
            spannableString.setSpan(
                StyleSpan(android.graphics.Typeface.BOLD),
                startIndex1,
                endIndex1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置黑色
            spannableString.setSpan(
                ForegroundColorSpan(Color.BLACK),
                startIndex1,
                endIndex1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

    /*    // 查找 "Daily Check-in" 文本
        val targetText2 = "Daily Check-in"
        val startIndex2 = fullText.indexOf(targetText2)
        if (startIndex2 != -1) {
            val endIndex2 = startIndex2 + targetText2.length

            // 设置加粗
            spannableString.setSpan(
                StyleSpan(android.graphics.Typeface.BOLD),
                startIndex2,
                endIndex2,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置橙色 #FF8C00
            spannableString.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF8C00")),
                startIndex2,
                endIndex2,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置下划线
            spannableString.setSpan(
                UnderlineSpan(),
                startIndex2,
                endIndex2,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 设置点击事件
            val clickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    // TODO: 实现点击功能

                }

                override fun updateDrawState(ds: android.text.TextPaint) {
                    super.updateDrawState(ds)
                    ds.color = Color.parseColor("#FF8C00")
                    ds.isUnderlineText = true
                }
            }

            spannableString.setSpan(
                clickableSpan,
                startIndex2,
                endIndex2,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }*/

        binding.tvHowToGetContent1.text = spannableString
        binding.tvHowToGetContent1.movementMethod = LinkMovementMethod.getInstance()
    }

    override fun initListener() {
        super.initListener()

        // Match Card按钮点击事件
        binding.btnMatchCard.click {
            val currentActivity = activity ?: return@click

            // 启动 MainActivity 并切换到首页
            if (currentActivity is MyCardActivity) {
                val intent = Intent(currentActivity, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    putExtra("target_tab_index", 0) // 0 = HomeFragment
                }
                currentActivity.startActivity(intent)
                currentActivity.finish()
            } else {
                // 如果已经在MainActivity中，直接切换Tab
                (currentActivity as? MainActivity)?.switchToTab(0)
            }
        }
        // Sample Picture按钮点击事件
        binding.ivSamplePicture.click {
            val currentActivity = activity ?: return@click

            // 启动 MainActivity 并切换到首页
            if (currentActivity is MyCardActivity) {
                val intent = Intent(currentActivity, MainActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    putExtra("target_tab_index", 0)
                }
                currentActivity.startActivity(intent)
                currentActivity.finish()
            } else {
                // 如果已经在MainActivity中，直接切换Tab
                (currentActivity as? MainActivity)?.switchToTab(0)
            }
            
        }
    }
}