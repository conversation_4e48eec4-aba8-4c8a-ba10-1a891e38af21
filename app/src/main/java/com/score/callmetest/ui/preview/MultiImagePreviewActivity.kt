package com.score.callmetest.ui.preview

import android.app.Activity
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.content.IntentCompat
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.github.chrisbanes.photoview.PhotoView
import com.score.callmetest.R
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.StatusBarUtils

class MultiImagePreviewActivity : Activity() {

    private lateinit var viewPager: ViewPager2
    private lateinit var dotsLayout: LinearLayout
    private lateinit var imageUris: ArrayList<Uri>

    override fun onCreate(savedInstanceState: Bundle?) {
        // 设置全屏沉浸模式
        StatusBarUtils.setFullscreenImmersive(this)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_multi_image_preview)

        viewPager = findViewById(R.id.view_pager)
        dotsLayout = findViewById(R.id.dots_layout)

        imageUris = IntentCompat.getParcelableArrayListExtra(intent,"imageUris", Uri::class.java) ?: arrayListOf()
        val startIndex = intent.getIntExtra("startIndex", 0)

        viewPager.adapter = object : RecyclerView.Adapter<PhotoViewHolder>() {
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
                val photoView = PhotoView(parent.context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    scaleType = ImageView.ScaleType.FIT_CENTER
                    setOnClickListener { (context as? Activity)?.finish() }
                }
                return PhotoViewHolder(photoView)
            }

            override fun getItemCount() = imageUris.size

            override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
                GlideUtils.load(
                    view = holder.photoView,
                    url = imageUris[position],
                    placeholder = R.drawable.image_placeholder,
                    error = R.drawable.image_placeholder
                )
            }
        }

        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateDots(position)
            }
        })

        viewPager.setCurrentItem(startIndex, false)
        viewPager.post { updateDots(startIndex) }
    }

    private fun updateDots(currentIndex: Int) {
        dotsLayout.removeAllViews()
        if (imageUris.size <= 1) {
            dotsLayout.visibility = View.GONE
            return
        } else {
            dotsLayout.visibility = View.VISIBLE
        }

        for (i in imageUris.indices) {
            val dotSize = DisplayUtils.dp2px(5f)
            val dot = ImageView(this)
            val params = LinearLayout.LayoutParams(dotSize, dotSize).apply {
                if (i > 0) {
                    marginStart = DisplayUtils.dp2px(3f)
                }
            }
            dot.layoutParams = params

            val isSelected = i == currentIndex
            val color = if (isSelected) Color.WHITE else "#88FFFFFF".toColorInt()

            dot.setImageDrawable(
                DrawableUtils.createRoundRectDrawable(
                    radius = dotSize / 2f,
                    color = color
                )
            )
            dotsLayout.addView(dot)
        }
    }

    class PhotoViewHolder(val photoView: PhotoView) : RecyclerView.ViewHolder(photoView)
}