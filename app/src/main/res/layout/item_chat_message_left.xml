<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingTop="4dp"
    android:paddingEnd="60dp"
    android:paddingBottom="4dp">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_avatar" />

    <!-- 用户名称 -->
<!--    <TextView
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/gray_666"
        android:visibility="gone"
        android:textSize="12sp"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="User Name" />-->

    <!-- 消息内容 - 文本 -->
    <com.score.callmetest.ui.widget.AlphaTextView
        android:id="@+id/tvMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_chat_bubble_left"
        android:maxWidth="237dp"
        android:padding="11dp"
        android:textColor="@color/black"
        android:textColorLink="@color/blue_chat_link"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="这是一条接收到的消息内容" />

    <!-- 图片消息 -->
    <com.score.callmetest.ui.widget.AspectRatioImageView
        android:id="@+id/ivImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/image_message"
        android:scaleType="fitCenter"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/image_placeholder"
        tools:visibility="visible" />

    <!-- 翻译按钮 -->
    <com.score.callmetest.ui.widget.AlphaTextView
        android:id="@+id/tvTranslate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6dp"
        android:drawableStart="@drawable/translate"
        android:drawablePadding="6dp"
        android:text="@string/click_to_translate"
        android:textColor="@color/gray_chat_translate"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@id/tvMessage"
        tools:visibility="visible" />

    <!-- 翻译进度 -->
    <ProgressBar
        android:id="@+id/progressTranslate"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="4dp"
        android:indeterminateTint="@color/gray_999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvTranslate"
        app:layout_constraintStart_toEndOf="@id/tvTranslate"
        app:layout_constraintTop_toTopOf="@id/tvTranslate" />

    <!-- 翻译内容 -->
    <LinearLayout
        android:id="@+id/translateParent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_chat_bubble_left"
        android:orientation="vertical"
        android:padding="11dp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/tvOriginMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="215dp"
            android:textColor="@color/black"
            android:textColorLink="@color/blue_chat_link"
            android:textSize="14sp"
            tools:text="这是一条接收到的消息内容这是一条接收到的消息内容这是一条接收到的消息内容这是一条接收到的消息内容这是一条接收到的消息内容" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@color/gray_chat_translated" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTranslateContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="215dp"
            android:layout_marginTop="10dp"
            android:textColor="@color/gray_chat_translated"
            android:textSize="14sp"
            tools:text="翻译内容翻译内容翻译内容翻译内容翻译内容翻译内容翻译内容" />

    </LinearLayout>

    <!-- 时间 === 暂时不需要显示时间 -->
    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        android:textColor="@color/gray_999"
        android:visibility="gone"
        android:textSize="10sp"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@id/tvMessage"
        tools:ignore="SmallSp"
        tools:text="10:30" />

</androidx.constraintlayout.widget.ConstraintLayout> 