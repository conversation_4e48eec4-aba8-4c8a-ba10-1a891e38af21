<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="60dp"
    android:paddingTop="4dp"
    android:paddingEnd="8dp"
    android:paddingBottom="4dp">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="8dp"
        android:contentDescription="@string/avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_avatar" />

    <!--  消息内容  -->
    <FrameLayout
        android:id="@+id/contentParent"
        android:layout_width="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintEnd_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="wrap_content">
        <!-- 文本 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/tvMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_chat_bubble_right"
            android:layout_gravity="end|top"
            android:maxWidth="237dp"
            android:padding="11dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:visibility="visible"
            tools:text="这是一条发送的消息内容11111111112f1ddddddddddddddddddddd" />

        <!-- 图片消息 -->
        <com.score.callmetest.ui.widget.AspectRatioImageView
            android:id="@+id/ivImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/image_message"
            android:scaleType="fitCenter"
            android:visibility="gone"
            tools:src="@drawable/image_placeholder"
            tools:visibility="visible" />

    </FrameLayout>

    <!-- 发送中状态 -->
    <ProgressBar
        android:id="@+id/progressSending"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="4dp"
        android:indeterminateTint="@color/gray_999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/contentParent"
        app:layout_constraintEnd_toStartOf="@+id/contentParent"
        app:layout_constraintTop_toTopOf="@+id/contentParent"
        tools:visibility="visible" />

    <!-- 状态图标 -->
    <ImageView
        android:id="@+id/ivStatus"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="4dp"
        android:contentDescription="@string/message_status"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/contentParent"
        app:layout_constraintEnd_toStartOf="@+id/contentParent"
        app:layout_constraintTop_toTopOf="@+id/contentParent"
        tools:src="@drawable/ic_sent" />

    <!-- 重发按钮 -->
    <ImageView
        android:id="@+id/ivResend"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="4dp"
        android:contentDescription="@string/resend"
        android:src="@drawable/chat_alert"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/contentParent"
        app:layout_constraintEnd_toStartOf="@+id/contentParent"
        app:layout_constraintTop_toTopOf="@+id/contentParent" />

    <!-- 时间==暂时不需要显示时间 -->
    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="8dp"
        android:textColor="@color/gray_999"
        android:visibility="gone"
        android:textSize="10sp"
        app:layout_constraintEnd_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@+id/contentParent"
        tools:ignore="SmallSp"
        tools:text="10:30" />

</androidx.constraintlayout.widget.ConstraintLayout> 