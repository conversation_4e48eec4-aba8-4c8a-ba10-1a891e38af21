<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-30dp"
        android:background="@drawable/flash_welcome_dialog_bg">

        <TextView
            android:id="@+id/welcome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="41dp"
            android:layout_marginTop="106dp"
            android:lineHeight="18dp"
            android:text="WELCOME YOU!"
            android:fontFamily="@font/futura_black_bold"
            android:textColor="@android:color/white"
            android:textFontWeight="13"
            android:textSize="21sp"
            android:textStyle="bold"
            android:typeface="sans" />

        <LinearLayout
            android:id="@+id/content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="42dp"
            android:layout_marginTop="141dp"
            android:layout_marginBottom="17dp"
            android:background="@drawable/bg_dialog_rounded"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="25dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Congratulations! "
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="95dp"
                android:layout_height="95dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="23dp"
                android:src="@drawable/match_card_gold" />

            <TextView
                android:id="@+id/btn_match"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="15dp"
                android:gravity="center"
                android:paddingVertical="16dp"
                android:text="Match Now"
                android:textColor="@android:color/white"
                android:textSize="17sp"
                android:textStyle="bold"
                android:typeface="sans" />
        </LinearLayout>

    </FrameLayout>
</FrameLayout>