<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingVertical="20dp">

    <!-- 加载中状态 -->
    <LinearLayout
        android:id="@+id/layout_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:visibility="gone"
        android:orientation="horizontal">

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="8dp"
            android:indeterminateTint="@color/refresh_loading" />

        <TextView
            android:id="@+id/tv_loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Loading..."
            android:textColor="@color/call_text_gray"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 加载完成状态 -->
    <LinearLayout
        android:id="@+id/layout_finished"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="visible">

        <View
            android:layout_width="4dp"
            android:layout_height="4dp"
            android:background="@drawable/shape_dot" />

        <TextView
            android:id="@+id/tv_bottom_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:text="@string/str_bottom"
            android:textColor="@color/call_text_gray"
            android:textSize="12sp" />

        <View
            android:layout_width="4dp"
            android:layout_height="4dp"
            android:background="@drawable/shape_dot" />

    </LinearLayout>

</LinearLayout>